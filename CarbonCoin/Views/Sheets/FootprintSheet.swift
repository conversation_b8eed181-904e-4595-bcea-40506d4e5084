//
//  FootprintSheet.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/3.
//

import SwiftUI
import PopupView

// MARK: - 出行打卡Sheet
struct FootprintSheet: View {
    @EnvironmentObject private var footprintsViewModel : FootprintsViewModel
    @EnvironmentObject private var appSettings: AppSettings
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态变量
    @State private var selectedTab = 0
    @State private var selectedActivityType: ActivityType = .walking
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var showSuccessAnimation = false // 打卡结束的动画效果
    @State private var showTrackingResult = false

    var body: some View {
        ZStack{
            NavigationView {
                VStack(spacing: 0) {
                    // MARK: - 顶部标题栏
                    headerView
                    
                    // MARK: - 标签页选择器
                    tabSelector
                    
                    // MARK: - 内容区域
                    TabView(selection: $selectedTab) {
                        // 第一个标签页 - 出行打卡
                        trackingTabView
                            .tag(0)
                        
                        // 第二个标签页 - 出行统计
                        statisticsTabView
                            .tag(1)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                }
                .background(.regularMaterial)
                .navigationBarHidden(true)
            }
            .onAppear {
                loadData()
            }
            .alert("提示", isPresented: $showAlert) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(alertMessage)
            }
            
            // 动画视图
            if showSuccessAnimation {
                ZStack {
                    // 背景遮罩层：全屏黑色半透明
                    Color.black.opacity(0.5)
                        .ignoresSafeArea(.all)

                    // 动画层
                    LottieHelperView(
                        fileName: "success.json",
                        contentMode: .scaleAspectFit,
                        playLoopMode: .playOnce,
                        animationProgress: 0.5
                    ) {
                        showSuccessAnimation = false
                    }
                    .frame(width: 200, height: 200)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .popup(isPresented: $showTrackingResult){
            // 本次出行打卡的结果
            trackingResultView()
        } customize: {
            $0
                .appearFrom(.centerScale)
                .closeOnTap(false)
                .backgroundColor(.black.opacity(0.4))
        }
    }

    // MARK: - 顶部标题栏
    private var headerView: some View {
        HStack {

            Spacer()

            Text("出行打卡")
                .font(.title2Brand)
                .foregroundColor(.textPrimary)

            Spacer()

        }
        .padding(.horizontal, Theme.Spacing.lg)
        .padding(.vertical, Theme.Spacing.md)
    }

    // MARK: - 标签页选择器
    private var tabSelector: some View {
        HStack(spacing: 0) {
            // 出行打卡标签
            Button(action: { selectedTab = 0 }) {
                VStack(spacing: Theme.Spacing.xs) {
                    Text("出行打卡")
                        .font(.bodyBrand)
                        .foregroundColor(selectedTab == 0 ? .textPrimary : .textSecondary)

                    Rectangle()
                        .fill(selectedTab == 0 ? Color.brandGreen : Color.clear)
                        .frame(height: 2)
                }
            }
            .frame(maxWidth: .infinity)

            // 出行统计标签
            Button(action: { selectedTab = 1 }) {
                VStack(spacing: Theme.Spacing.xs) {
                    Text("出行统计")
                        .font(.bodyBrand)
                        .foregroundColor(selectedTab == 1 ? .textPrimary : .textSecondary)

                    Rectangle()
                        .fill(selectedTab == 1 ? Color.brandGreen : Color.clear)
                        .frame(height: 2)
                }
            }
            .frame(maxWidth: .infinity)
        }
        .padding(.horizontal, Theme.Spacing.lg)
        .padding(.bottom, Theme.Spacing.md)
    }

    // MARK: - 第一个标签页 - 出行打卡
    private var trackingTabView: some View {
        ScrollView {
            VStack(spacing: Theme.Spacing.lg) {
                if footprintsViewModel.isTracking {
                    // 正在出行状态
                    currentTrackingView
                } else {
                    // 未出行状态
                    startTrackingView
                }
            }
            .padding(.horizontal, Theme.Spacing.lg)
            .padding(.vertical, Theme.Spacing.md)
        }
    }

    // MARK: - 开始出行视图
    private var startTrackingView: some View {
        VStack(spacing: Theme.Spacing.md) {
            // 标题文字
            VStack(spacing: Theme.Spacing.sm) {
                Text("请选择出行方式")
                    .font(.title2Brand)
                    .foregroundColor(.textPrimary)
                    .multilineTextAlignment(.center)

                Text("选择步行方式将会获得更多碳币哦～")
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.bottom, Theme.Spacing.lg)

            // 出行方式选择按钮
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: Theme.Spacing.md), count: 4), spacing: Theme.Spacing.md) {
                ForEach(ActivityType.allCases, id: \.self) { activityType in
                    ActivityTypeButton(
                        activityType: activityType,
                        isSelected: selectedActivityType == activityType,
                        action: {
                            selectedActivityType = activityType
                        }
                    )
                }
            }

            Spacer(minLength: Theme.Spacing.md)

            // 确认开始按钮
            Button(action: {
                startTracking()
            }) {
                HStack {
                    if footprintsViewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }

                    Text(footprintsViewModel.isLoading ? "正在开始..." : "确认开始")
                        .font(.bodyBrand)
                        .foregroundColor(.white)
                }
                .frame(maxWidth: UIScreen.main.bounds.size.width * 0.7)
                .padding(.vertical, Theme.Spacing.md)
                .background(
                    LinearGradient(
                        colors: [Color(hex: "61D7B9"), Color(hex: "B0EB67")],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .cornerRadius(Theme.CornerRadius.lg)
                .disabled(footprintsViewModel.isLoading)
            }
            .cardButtonStyle()
            
        }
    }

    // MARK: - 当前出行状态视图
    private var currentTrackingView: some View {
        VStack(spacing: Theme.Spacing.md) {
            // 当前出行信息卡片
            VStack(spacing: Theme.Spacing.md) {
                // 出行方式显示
                HStack {
                    Image(getActivityIcon(selectedActivityType))
                        .renderingMode(.template)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: Theme.IconSize.xl, height: Theme.IconSize.xl)
                        .foregroundColor(.textPrimary)

                    VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                        Text("正在\(selectedActivityType.displayName)")
                            .font(.title3Brand)
                            .foregroundColor(.textPrimary)

                        Text("出行进行中...")
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                    }

                    Spacer()
                }

                // 出行数据显示
                if let currentFootprints = footprintsViewModel.currentFootprints {
                    HStack(spacing: Theme.Spacing.md) {
                        // 出行时间
                        VStack(spacing: Theme.Spacing.xs) {
                            Text("出行时间")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)

                            Text(currentFootprints.formattedDuration)
                                .font(.title3Brand)
                                .foregroundColor(.brandGreen)
                        }
                        .padding()

                        Spacer()

                        // 出行距离
                        VStack(spacing: Theme.Spacing.xs) {
                            Text("出行距离")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)

                            Text(currentFootprints.formattedDistance)
                                .font(.title3Brand)
                                .foregroundColor(.brandGreen)
                        }
                        .padding()
                    }
                }
            }
            .padding(Theme.Spacing.lg)
            .glassCard()

            Spacer(minLength: Theme.Spacing.md)

            // 结束打卡按钮
            Button(action: {
                stopTracking()
            }) {
                HStack {
                    if footprintsViewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }

                    Text("结束打卡")
                        .font(.bodyBrand)
                        .foregroundColor(.white)
                }
                .frame(maxWidth: UIScreen.main.bounds.size.width * 0.7)
                .padding(.vertical, Theme.Spacing.md)
                .background(Color.error)
                .cornerRadius(Theme.CornerRadius.lg)
                .disabled(footprintsViewModel.isLoading)
            }
            .cardButtonStyle()
        }
    }
    
    // MARK: 出行打卡结果popup
    private var trackingResultView: some View {
        
    }

    // MARK: - 第二个标签页 - 出行统计
    private var statisticsTabView: some View {
        ScrollView {
            VStack(spacing: Theme.Spacing.lg) {
                // 统计数据卡片
                if let stats = footprintsViewModel.footprintsStats {
                    statisticsCardView(stats: stats)
                } else {
                    // 加载统计数据
                    VStack(spacing: Theme.Spacing.md) {
                        if footprintsViewModel.isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .brandGreen))

                            Text("正在加载统计数据...")
                                .font(.bodyBrand)
                                .foregroundColor(.textSecondary)
                        } else {
                            Text("暂无统计数据")
                                .font(.bodyBrand)
                                .foregroundColor(.textSecondary)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding(Theme.Spacing.xl)
                    .glassCard()
                }

                // 出行记录列表
                footprintsListView
            }
            .padding(.horizontal, Theme.Spacing.lg)
            .padding(.vertical, Theme.Spacing.md)
        }
    }

    // MARK: - 统计数据卡片视图
    private func statisticsCardView(stats: FootprintsStatsResponse) -> some View {
        VStack(spacing: Theme.Spacing.lg) {
            // 总体统计
            HStack(spacing: Theme.Spacing.lg) {
                StatisticItem(title: "总出行", value: "\(stats.totalTrips)", subtitle: "次")
                StatisticItem(title: "总距离", value: String(format: "%.1f", stats.totalDistance), subtitle: "公里")
                StatisticItem(title: "总时长", value: String(format: "%.1f", stats.totalDuration), subtitle: "小时")
            }

            Divider()
                .background(Color.textSecondary.opacity(0.3))

            // 各出行方式统计
            VStack(spacing: Theme.Spacing.md) {
                Text("出行方式统计")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                VStack(spacing: Theme.Spacing.sm) {
                    ActivityStatRow(
                        activityType: .walking,
                        stat: stats.activityStats.walking
                    )
                    ActivityStatRow(
                        activityType: .cycling,
                        stat: stats.activityStats.cycling
                    )
                    ActivityStatRow(
                        activityType: .bus,
                        stat: stats.activityStats.bus
                    )
                    ActivityStatRow(
                        activityType: .subway,
                        stat: stats.activityStats.subway
                    )
                }
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }

    // MARK: - 出行记录列表视图
    private var footprintsListView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("出行记录")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            if footprintsViewModel.footprintsList.isEmpty {
                // 空状态
                VStack(spacing: Theme.Spacing.md) {
                    Image(systemName: "figure.walk.circle")
                        .font(.system(size: 48))
                        .foregroundColor(.textSecondary)

                    Text("暂无出行记录")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)

                    Text("开始你的第一次出行打卡吧！")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(Theme.Spacing.xl)
                .glassCard()
            } else {
                // 记录列表
                LazyVStack(spacing: Theme.Spacing.sm) {
                    ForEach(groupedFootprints, id: \.key) { group in
                        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                            // 分组标题
                            Text(group.key)
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)
                                .padding(.horizontal, Theme.Spacing.md)

                            // 该分组的记录
                            ForEach(group.value) { footprint in
                                FootprintRecordRow(footprint: footprint)
                            }
                        }
                    }
                }
            }
        }
    }

    // MARK: - 辅助方法

    /// 加载数据
    private func loadData() {
        Task {
            await footprintsViewModel.loadFootprintsList(userId: appSettings.userId)
            await footprintsViewModel.loadFootprintsStats(userId: appSettings.userId)
        }
    }

    /// 开始出行打卡
    private func startTracking() {
        Task {
            await footprintsViewModel.startTracking(
                userId: appSettings.userId,
                activityType: selectedActivityType
            )

            if let errorMessage = footprintsViewModel.errorMessage {
                alertMessage = errorMessage
                showAlert = true
            }
        }
    }

    /// 结束出行打卡
    private func stopTracking() {
        Task {
            await footprintsViewModel.stopTracking()
            
            

            if let errorMessage = footprintsViewModel.errorMessage {
                alertMessage = errorMessage
                showAlert = true
            } else {
                // 显示动画和打卡结果
                showSuccessAnimation = true
                showTrackingResult = true
                
                // 重新加载数据
                await loadData()
            }
        }
    }

    /// 获取出行方式图标
    private func getActivityIcon(_ activityType: ActivityType) -> String {
        switch activityType {
        case .walking:
            return "log-walk"
        case .cycling:
            return "log-cycling"
        case .bus:
            return "log-bus"
        case .subway:
            return "log-subway"
        }
    }

    /// 按时间分组的出行记录
    private var groupedFootprints: [(key: String, value: [UserFootprints])] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: footprintsViewModel.footprintsList) { footprint in
            let components = calendar.dateComponents([.year, .month, .weekOfYear], from: footprint.createdAt)
            if let year = components.year, let month = components.month {
                return "\(year)年\(month)月"
            }
            return "其他"
        }

        return grouped.sorted { $0.key > $1.key }
    }
}

// MARK: - 出行方式选择按钮
private struct ActivityTypeButton: View {
    let activityType: ActivityType
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: Theme.Spacing.xs) {
                // 图标
                Image(getIconName(activityType))
                    .renderingMode(.template)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: Theme.IconSize.md, height: Theme.IconSize.md)
                    .foregroundColor(isSelected ? .black : .textSecondary)

                // 文字
                Text(activityType.displayName)
                    .font(.captionBrand)
                    .foregroundColor(isSelected ? .black : .textSecondary)
                    .lineLimit(1)
            }
//            .frame(width: 64)
            .padding(.horizontal, Theme.Spacing.md)
            .padding(.vertical, Theme.Spacing.md)
            .background(
                Capsule()
                    .fill(
                        isSelected ?
                        LinearGradient(
                            gradient: Gradient(colors: [Color.brandGreen, Color(hex: "B0EB67")]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ) :
                        LinearGradient(
                            gradient: Gradient(colors: [Color.cardBackground, Color.cardBackground]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        Capsule()
                            .stroke(
                                isSelected ? Color.clear : Color.textSecondary.opacity(0.3),
                                lineWidth: 1
                            )
                    )
            )
        }
        .cardButtonStyle()
    }

    private func getIconName(_ activityType: ActivityType) -> String {
        switch activityType {
        case .walking:
            return "log-walk"
        case .cycling:
            return "log-cycling"
        case .bus:
            return "log-bus"
        case .subway:
            return "log-subway"
        }
    }
}

// MARK: - 统计项组件
private struct StatisticItem: View {
    let title: String
    let value: String
    let subtitle: String

    var body: some View {
        VStack(spacing: Theme.Spacing.xs) {
            Text(title)
                .font(.captionBrand)
                .foregroundColor(.textSecondary)

            HStack(alignment: .bottom, spacing: 2) {
                Text(value)
                    .font(.title2Brand)
                    .foregroundColor(.brandGreen)

                Text(subtitle)
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 出行方式统计行
private struct ActivityStatRow: View {
    let activityType: ActivityType
    let stat: FootprintsStatsResponse.ActivityStats.ActivityStat

    var body: some View {
        HStack {
            // 图标
            Image(getIconName(activityType))
                .renderingMode(.template)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: Theme.IconSize.md, height: Theme.IconSize.md)
                .foregroundColor(.textSecondary)

            // 出行方式名称
            Text(activityType.displayName)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)

            Spacer()

            // 统计数据
            HStack(spacing: Theme.Spacing.lg) {
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(stat.trips)")
                        .font(.captionBrand)
                        .foregroundColor(.brandGreen)
                    Text("次")
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                }

                VStack(alignment: .trailing, spacing: 2) {
                    Text(String(format: "%.1f", stat.distance))
                        .font(.captionBrand)
                        .foregroundColor(.brandGreen)
                    Text("公里")
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                }

                VStack(alignment: .trailing, spacing: 2) {
                    Text(String(format: "%.1f", stat.duration))
                        .font(.captionBrand)
                        .foregroundColor(.brandGreen)
                    Text("小时")
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                }
            }
        }
        .padding(.horizontal, Theme.Spacing.sm)
        .padding(.vertical, Theme.Spacing.xs)
    }

    private func getIconName(_ activityType: ActivityType) -> String {
        switch activityType {
        case .walking:
            return "log-walk"
        case .cycling:
            return "log-cycling"
        case .bus:
            return "log-bus"
        case .subway:
            return "log-subway"
        }
    }
}

// MARK: - 出行记录行
private struct FootprintRecordRow: View {
    let footprint: UserFootprints

    var body: some View {
        NavigationLink(destination: FootprintDetailView(footprint: footprint)){
            HStack(spacing: Theme.Spacing.md) {
                // 出行方式图标
                Image(getIconName(footprint.activityType))
                    .renderingMode(.template)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: Theme.IconSize.md, height: Theme.IconSize.md)
                    .foregroundColor(.brand)
                    .padding(8)
                    .background(Color.brand.opacity(0.1))
                    .clipShape(Circle())
                
                // 出行信息
                VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                    HStack {
                        Text(footprint.activityType.displayName)
                            .font(.bodyBrand)
                            .foregroundColor(.textPrimary)
                        
                        Spacer()
                        
                        Text(footprint.formattedCreatedAt)
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                    }
                    
                    HStack {
                        Text("距离: \(footprint.formattedDistance)")
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                        
                        Spacer()
                        
                        Text("时长: \(footprint.formattedDuration)")
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                        
                        // 完成状态
                        if footprint.isFinished {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.success)
                                .font(.caption)
                        } else {
                            Image(systemName: "clock.fill")
                                .foregroundColor(.warning)
                                .font(.caption)
                        }
                    }
                }
            }
            .padding(Theme.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                    .fill(Color.cardBackground.opacity(0.5))
                    .overlay(
                        RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                            .stroke(Color.textSecondary.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(.plain)
    }

    private func getIconName(_ activityType: ActivityType) -> String {
        switch activityType {
        case .walking:
            return "log-walk"
        case .cycling:
            return "log-cycling"
        case .bus:
            return "log-bus"
        case .subway:
            return "log-subway"
        }
    }
}

// MARK: - 预览
#Preview {
    FootprintSheet()
        .environmentObject(AppSettings())
        .environmentObject(FootprintsViewModel())
}
